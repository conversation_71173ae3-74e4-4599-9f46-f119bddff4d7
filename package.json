{"name": "raven-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@reduxjs/toolkit": "^1.9.6", "@tailwindcss/forms": "^0.5.6", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.15", "axios": "^1.5.1", "caniuse-lite": "^1.0.30001668", "connected-react-router": "^6.9.3", "framer-motion": "^10.16.4", "heroicons-react": "^1.4.1", "i18next": "^23.5.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "msw": "^2.0.1", "path": "^0.12.7", "postcss": "^8.4.29", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-i18next": "^13.2.2", "react-qr-code": "^2.0.15", "react-redux": "^8.1.2", "react-router-dom": "^6.27.0", "recharts": "^2.9.3", "redux": "^4.2.1", "redux-logger": "^3.0.6", "rough-notation": "^0.5.1", "tailwindcss": "^3.3.3"}, "devDependencies": {"@types/js-cookie": "^3.0.4", "@types/lodash": "^4.14.199", "@types/node": "^20.6.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/redux-logger": "^3.0.10", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "tailwind-scrollbar": "^3.0.5", "typescript": "^5.0.2", "vite": "^4.4.5"}, "msw": {"workerDirectory": "public"}}