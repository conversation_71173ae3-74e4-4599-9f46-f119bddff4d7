import RevenueChart from '@/features/charts/RevenueChart';
import useGetMonthlyOverview from '@/hooks/useGetMonthlyOverview';

interface MonthlyOverviewUtils {
  isLoading: boolean;
  isUninitialized: boolean;
  data: Record<string, unknown>;
}

const EarningOverviewChart = () => {
  const { overview = [], loading } = useGetMonthlyOverview({
    select: ({ isLoading, isUninitialized, data }: MonthlyOverviewUtils) => ({
      loading: isLoading || isUninitialized,
      overview: data?.overview,
    }),
  });

  return (
    <div className="bg-white p-5">
      <RevenueChart
        title="dashboard.monthlyOverview"
        loading={loading}
        data={overview}
      />
    </div>
  );
};

export default EarningOverviewChart;
