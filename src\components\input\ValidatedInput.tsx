import { FC, JSX } from 'react';
import { FieldErrors, UseFormRegister } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Translate } from 'components/translate';
import { SignupFormData } from '@/models/signup-form-data';
import { SimpleText } from 'components/typography';
import clsx from '@/features/utils/clsx';

interface ValidatedInputProps {
  msgId: string;
  ariaLabel: string;
  field: string;
  type: string;
  inputClass: string;
  className: string;
  placeholder?: string;
  hint?: string;
  register: UseFormRegister<SignupFormData>;
  errors: FieldErrors<SignupFormData>;
}

/**
 * ValidatedInput component.
 *
 * This component provides an input field with validation and error messages.
 *
 * @component
 *
 * @param {ValidatedInputProps} props - The component props.
 * @param {string} props.msgId - The translation key for the label.
 * @param {string} props.ariaLabel - The ARIA label for the input.
 * @param {string} props.field - The field name used for registration with React Hook Form.
 * @param {string} props.type - The input type (e.g., 'text', 'email').
 * @param {string} props.inputClass - The CSS class for the input element.
 * @param {string} props.className - The CSS class for the component container.
 * @param {string} [props.placeholder] - The placeholder text for the input.
 * @param {string} [props.hint] - A hint or description for the input.
 * @param {UseFormRegister<SignupFormData>} props.register - React Hook Form's register function.
 * @param {FieldErrors<SignupFormData>} props.errors - Validation errors from React Hook Form.
 *
 * @returns {JSX.Element} The ValidatedInput component.
 */
const ValidatedInput: FC<ValidatedInputProps> = (
  props: ValidatedInputProps
): JSX.Element => {
  const { t } = useTranslation();

  return (
    <div className={props.className}>
      <label className="form-label dark:text-white">
        <Translate msgId={props.msgId} />
      </label>
      <input
        className={clsx(
          props.inputClass,
          'dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500'
        )}
        type={props.type}
        {...props.register(props.field)}
        aria-label={props.ariaLabel}
        {...(props.placeholder ? { placeholder: t(props.placeholder) } : {})}
      />
      {props.hint && (
        <span className="text-sm text-gray-400">
          <Translate msgId={props.hint} />
        </span>
      )}
      {props.errors[props.field] && (
        <SimpleText component="small" className="text-red-500">
          <Translate msgId={(props.errors[props.field]!.message as string)!} />
        </SimpleText>
      )}
    </div>
  );
};

export default ValidatedInput;
