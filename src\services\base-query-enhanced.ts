import { unsetCredentials } from 'features/Auth/authSlice';
import { BaseQueryFn, FetchArgs, FetchBaseQueryError, retry } from '@reduxjs/toolkit/query/react';
import getErrors from '@/features/utils/get-errors';
import baseQuery from './base-query';

/**
 * Anytime you see a process.env.NODE_ENV === 'development', it's so I can log things into the console. I don't have
 * a sweet redux logger.
 */

const baseQueryEnhanced: BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError> = async (args, api, extraOptions) => {

    if (process.env.NODE_ENV === 'development') {
        console.info('Fetching', args);
    }

    const result = await baseQuery(args, api, extraOptions);

    if (process.env.NODE_ENV === 'development') {
        console.info('Fetched');
    }

    if (result.error) {
        result.error.data = getErrors(result.error.data);

        if (process.env.NODE_ENV === 'development') {
            console.info('Fetch Error!', result);
        }

        switch (result.error?.status) {
            case 401:
                api.dispatch(unsetCredentials());
                retry.fail(result.error);
                break;
            case 400:
                retry.fail(result.error);
                break;
            default:
        }
    }

    return result;
};

const enhancedWithRetry = retry(baseQueryEnhanced, {
    maxRetries: 5,
});

export default enhancedWithRetry;