import { useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import { AppDispatch } from '@/store/store';
import { SignupFormData } from '@/models/signup-form-data';
import {
  getError,
  registerUser,
  reset,
  setCountry,
  getCountry,
} from '../SignupSlice';
import { resolver } from 'features/utils/signup-form.resolver';
import { Translate } from '@/components/translate';
import CountrySelector from '@/components/selector/CountrySelector';
import { COUNTRIES } from '@/data/countries';
import { SelectMenuOption } from '@/data/types';
import ValidatedInput from '@/components/input/ValidatedInput';
import { Button } from '@/components/button';

const SignupForm = () => {
  const dispatch: AppDispatch = useDispatch();
  const error = useSelector(getError);

  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<SignupFormData>({ resolver });

  const onSubmit = useCallback(
    (data: SignupFormData) => {
      dispatch(reset());
      dispatch(registerUser({ data }));
    },

    [dispatch]
  );

  const handleCountrySelector = (event: any) => {
    dispatch(setCountry(event));
  };

  const [isOpen, setIsOpen] = useState(false);
  // Default this to a country's code to preselect it
  const country = useSelector(getCountry);

  return (
    <form className="text-left" onSubmit={handleSubmit(onSubmit)}>
      {!!error && (
        <div className="bg-red-50 dark:bg-red-900 px-3 py-1 mb-3">
          <p className="text-red-500 dark:text-red-200 text-center m-0">
            {error.errors.map((err, index) => (
              <span key={index}>{err.message}</span>
            ))}
          </p>
        </div>
      )}
      <div className="md:flex md:flex gap-2 mb-4">
        <ValidatedInput
          register={register}
          errors={errors}
          className="mb-3 form-group flex flex-col md:w-1/2 w-full"
          field="firstName"
          type="text"
          inputClass={`form-control form-input w-full${
            errors.firstName ||
            error?.errors.some((err) => err.field === 'firstName')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.firstName"
          ariaLabel="Enter first name"
          placeholder="Kofi"
        />
        <ValidatedInput
          register={register}
          errors={errors}
          className="mb-3 form-group flex flex-col md:w-1/2 w-full"
          field="otherNames"
          type="text"
          inputClass={`form-control form-input w-full${
            errors.otherNames ||
            error?.errors.some((err) => err.field === 'otherNames')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.otherNames"
          ariaLabel="Enter your other names"
          placeholder="Sintim"
        />
        <ValidatedInput
          register={register}
          errors={errors}
          className="mb-3 form-group flex flex-col md:w-1/2 w-full"
          field="lastName"
          type="text"
          inputClass={`form-control form-input w-full${
            errors.lastName ||
            error?.errors.some((err) => err.field === 'lastName')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.lastName"
          ariaLabel="Enter last name"
          placeholder="Obeng"
        />
      </div>
      <div className="md:flex md:flex gap-2 mb-4">
        <ValidatedInput
          register={register}
          errors={errors}
          className="mb-3 form-group flex flex-col md:w-1/2 w-full"
          field="phoneNumber"
          type="tel"
          inputClass={`form-control form-input w-full${
            errors.phoneNumber ||
            error?.errors.some((err) => err.field === 'phoneNumber')
              ? ' error-field'
              : ''
          }`}
          msgId="auth.phoneNumber"
          ariaLabel="Enter company phone number"
        />
        <div className="mb-3 form-group flex flex-col md:w-1/2">
          <label className="form-label">
            <Translate msgId="auth.country" />
          </label>
          <CountrySelector
            id={'countries'}
            open={isOpen}
            onToggle={() => setIsOpen(!isOpen)}
            onChange={handleCountrySelector}
            // We use this type assertion because we are always sure this find will return a value but need to let TS know since it could technically return null
            selectedValue={
              COUNTRIES.find(
                (option) => option.value === country
              ) as SelectMenuOption
            }
          />
        </div>
      </div>
      <ValidatedInput
        register={register}
        errors={errors}
        className="mb-7 form-group flex flex-col"
        field="email"
        type="email"
        inputClass={`form-control form-input w-full${
          errors.email || error?.errors.some((err) => err.field === 'email')
            ? ' error-field'
            : ''
        }`}
        msgId="auth.companyEmail"
        ariaLabel="Enter company email"
        placeholder="auth.email"
      />
      <ValidatedInput
        register={register}
        errors={errors}
        className="mb-7 form-group flex flex-col"
        field="password"
        type="password"
        inputClass={`form-control form-input w-full${
          errors.password ||
          error?.errors.some((err) => err.field === 'password')
            ? ' error-field'
            : ''
        }`}
        msgId="auth.password"
        ariaLabel="Enter a password"
        hint="auth.passMin"
      />

      <ValidatedInput
        register={register}
        errors={errors}
        className="mb-8 form-group flex flex-col"
        field="confirmPassword"
        type="password"
        inputClass={`form-control form-input w-full${
          errors.confirmPassword ||
          error?.errors.some((err) => err.field === 'confirmPassword')
            ? ' error-field'
            : ''
        }`}
        msgId="auth.confirmPassword"
        ariaLabel="Confirm your password"
      />
      <Button
        onClick={handleSubmit(onSubmit)}
        variant="primary"
        type="submit"
        className="w-full rounded mb-8"
      >
        <Translate msgId="auth.signup" />
      </Button>
    </form>
  );
};

export default SignupForm;
