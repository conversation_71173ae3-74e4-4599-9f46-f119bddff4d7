import {
  Outlet,
  Route,
  BrowserRouter as Router,
  Routes
} from 'react-router-dom';
import { Suspense, lazy } from 'react';
import ScrollToTop from '@/features/utils/ScrollToTop';
import ErrorBoundary from 'components/error/ErrorBoundary';
import ProtectedRoute from './routes/ProtectedRoute';
import Dashboard from '@/routes/Dashboard/Dashboard';
import { DashboardRoute } from './layouts/dashboard';
import NotificationsProvider from './features/Notifications/Provider';
import Loader from './components/loader/Loader';
import NotFound from './Pages/NotFound/NotFound';

// import { GenerateBackgroundCircles } from 'components/circles';
const AdminPage = lazy(() => import('Pages/Admin/AdminPage'));

const LandingPage = lazy(() => import('Pages/Landing/LandingPage'));
const BusinessPage = lazy(() => import('Pages/Landing/BusinessPage'));
const EnterprisePage = lazy(() => import('Pages/Landing/EnterprisePage'));
const PaymentsPage = lazy(() => import('Pages/Landing/PaymentsPage'));
const PricingPage = lazy(() => import('Pages/Landing/PricingPage'));
const SupportPage = lazy(() => import('Pages/Landing/SupportPage'));
const CardPage = lazy(() => import('Pages/Landing/CardPage'));
const CareersPage = lazy(() => import('Pages/Landing/Careers'));
const AboutUsPage = lazy(() => import('Pages/Landing/AboutUs'));
const ResourcesPage = lazy(() => import('Pages/Landing/Resources'));
const DocumentationPage = lazy(() => import('Pages/Landing/Documentation'));
const DocsPage = lazy(() => import('Pages/Landing/Docs'));
const SignupPage = lazy(() => import('Pages/Signup/SignupPage'));
const VerifyOtp = lazy(() => import('Pages/VerifyOTP/VerifyOtp'));
const SigninPage = lazy(() => import('Pages/Signin/SigninPage'));

const DashboardPayments = lazy(
  () => import('Pages/Payments/DashboardPayments')
);
const GetPaid = lazy(() => import('Pages/Payments/GetPaid'));
const Pay = lazy(() => import('Pages/Payments/Pay'));
const ConfirmWithdrawal = lazy(
  () => import('Pages/Payments/ConfirmWithdrawal')
);
const SelectPaymentLinkPlan = lazy(
  () => import('Pages/Payments/SelectPaymentLinkPlan')
);
const GeneratePaymentLink = lazy(
  () => import('Pages/Payments/GeneratePaymentLink')
);
const MakePayment = lazy(() => import('Pages/Payments/MakePayment'));
const TransactionsPage = lazy(
  () => import('Pages/Transactions/TransactionsPage')
);
const TransactionDetailsPage = lazy(
  () => import('Pages/Transactions/TransactionDetailsPage')
);
const RefundPage = lazy(() => import('@/Pages/Refund/RefundPage'));

const CustomersPage = lazy(() => import('Pages/Customers/CustomersPage'));
const VendorList = lazy(() => import('Pages/Customers/VendorList'));
const ClientList = lazy(() => import('Pages/Customers/ClientList'));

const SettingsPage = lazy(() => import('Pages/Settings/SettingsPage'));
const Profile = lazy(() => import('Pages/Settings/Profile'));
const Security = lazy(() => import('Pages/Settings/Security'));
const Accounts = lazy(() => import('Pages/Settings/Accounts'));
const Api = lazy(() => import('Pages/Settings/Api'));
const PaymentLink = lazy(() => import('Pages/Settings/PaymentLink'));
const Integration = lazy(() => import('Pages/Settings/Integration'));
const Preferences = lazy(() => import('Pages/Settings/Preferences'));
const StatisticsPage = lazy(() => import('Pages/Statistics/StatisticsPage'));

function App() {
  sessionStorage.setItem('session', JSON.stringify({}));
  sessionStorage.setItem('roles', JSON.stringify({}));
  sessionStorage.setItem('admin', 'true');
  sessionStorage.setItem('token', 'jgsdfkdfshkdfhu');
  sessionStorage.setItem('merchant', 'true');
  sessionStorage.setItem('viewAsMerchant', 'true');

  return (
    <ErrorBoundary>
      <NotificationsProvider />
      <Router>
        <ScrollToTop />
        <Suspense fallback={<Loader />}>
          <Routes>
            <Route path="/" Component={LandingPage} />
            <Route path="/payments" Component={PaymentsPage} />
            <Route path="/pricing" Component={PricingPage} />
            <Route path="/business" Component={BusinessPage} />
            <Route path="/enterprise" Component={EnterprisePage} />
            <Route path="/card" Component={CardPage} />
            <Route path="/careers" Component={CareersPage} />
            <Route path="/documentation" Component={DocumentationPage} />
            <Route path="/docs" Component={DocsPage} />
            <Route path="/about-us" Component={AboutUsPage} />
            <Route path="/resources" Component={ResourcesPage} />
            <Route path="/support" Component={SupportPage} />
            <Route path="/signup" Component={SignupPage} />
            <Route path="/signin" Component={SigninPage} />
            <Route
              path="/verify-otp"
              element={
                <ProtectedRoute redirectPath="/signup" rule={'otpCookie'}>
                  <VerifyOtp />
                </ProtectedRoute>
              }
            />
            <Route path="/confirm-withdrawal" element={<ConfirmWithdrawal />} />
            <Route path="/preview-payment-link" element={<MakePayment />} />
            <Route
              path="/select-payment-link-plan"
              element={<SelectPaymentLinkPlan />}
            />
            <Route
              path="/generate-payment-link"
              element={<GeneratePaymentLink />}
            />
            <Route
              path="/dashboard/*"
              Component={(props) => (
                <DashboardRoute
                  component={Dashboard}
                  redirectUnauthorized
                  {...props}
                />
              )}
            >
              <Route path="admin/*" element={<AdminPage />} />
              <Route
                path="admin/invite"
                element={<div>Here is the admin invite page</div>}
              />
              <Route
                path="admin/merchant"
                element={<div>Here is the admin merchant page</div>}
              />
              <Route path="payments" Component={() => <Outlet />}>
                <Route path="payment-methods" element={<DashboardPayments />} />
                <Route path="get-paid" element={<GetPaid />} />
                <Route path="" element={<Pay />} />
              </Route>

              <Route
                path="subscriptions"
                element={<div>Here is the susbscriptions page</div>}
              />
              <Route path="transactions/*" Component={Outlet}>
                <Route path="" element={<TransactionsPage />} />
                <Route path="refund" element={<RefundPage />} />
                <Route path=":id" element={<TransactionDetailsPage />} />
              </Route>
              <Route path="customers/*" Component={() => <CustomersPage />}>
                <Route path="vendors" element={<VendorList />} />
                <Route path="clients" element={<ClientList />} />
              </Route>
              <Route path="sales" element={<div>Here is the sales page</div>} />
              <Route path="settings/*" Component={() => <SettingsPage />}>
                <Route path="" element={<Profile />} />
                <Route path="profile" element={<Profile />} />
                <Route path="preferences" element={<Preferences />} />
                <Route path="security" element={<Security />} />
                <Route path="accounts" element={<Accounts />} />
                <Route path="payment-link" element={<PaymentLink />} />
                <Route path="api" element={<Api />} />
                <Route path="integration" element={<Integration />} />
              </Route>
              <Route path="statistics" element={<StatisticsPage />} />
            </Route>
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Suspense>
        {/* <GenerateBackgroundCircles /> */}
      </Router>
    </ErrorBoundary>
  );
}

export default App;
