import { SignupFormData } from '@/models/signup-form-data';
import { Resolver } from 'react-hook-form';

export const resolver: Resolver<SignupFormData> = async (values) => {
    const errors: any = {};
    // firstName validation
    if (!values.firstName) {
        errors.firstName = {
            type: 'required',
            message: 'auth.noFirstName',
        };
    } else if (values.firstName.length < 2) {
        errors.firstName = {
            type: 'minLength',
            message: 'auth.shortName',
        };
    } else if (values.firstName.length > 50) {
        errors.firstName = {
            type: 'maxLength',
            message: 'auth.longName',
        };
    }

    // Other names
    if (values.otherNames.length > 70) {
        errors.otherNames = {
            type: 'maxLength',
            message: 'auth.longName',
        };
    }

    // lastName validation
    if (!values.lastName) {
        errors.lastName = {
            type: 'required',
            message: 'auth.noLastName',
        };
    } else if (values.lastName.length < 2) {
        errors.lastName = {
            type: 'minLength',
            message: 'auth.shortName',
        };
    } else if (values.lastName.length > 50) {
        errors.lastName = {
            type: 'maxLength',
            message: 'auth.longName',
        };
    }

    // Email validation
    if (!values.email) {
        errors.email = {
            type: 'required',
            message: 'auth.enterCompanyEmail',
        };
        // test to validate email format
    } else if (!/^\S+@\S+\.\S+$/.test(values.email)) {
        errors.email = {
            type: 'pattern',
            message: 'auth.enterValidEmail',
        };
    }

    // phone number
    const phoneNumberRegex = /^(?:\+?1\s?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/;

    if (!phoneNumberRegex.test(values.phoneNumber)) {
        errors.phoneNumber = {
            type: 'pattern',
            message: 'auth.enterValidPhone'
        }
    }

    // // password validation
    // if (!values.password) {
    //     errors.password = {
    //         type: 'required',
    //         message: 'auth.enterPassword',
    //     };
    // } else if (values.password.length < 8) {
    //     errors.password = {
    //         type: 'minLength',
    //         message: 'auth.shortPassword',
    //     };
    // } else if (values.password.length > 50) {
    //     errors.password = {
    //         type: 'maxLength',
    //         message: 'auth.longPassword',
    //     };
    //     // check to make sure password contains at least one number
    // } else if (!/\d/.test(values.password)) {
    //     errors.password = {
    //         type: 'pattern',
    //         message: 'auth.passwordPattern',
    //     };
    // }

    // // Validation for password confirmation
    // if (!values.confirmPassword) {
    //     errors.confirmPassword = {
    //         type: 'required',
    //         message: 'auth.enterPasswordConfirm'
    //     }
    // } else if (values.confirmPassword !== values.password) {
    //     errors.confirmPassword = {
    //         type: 'pattern',
    //         message: 'auth.noMatchPassword',
    //     };
    // }

    return {
        values,
        errors,
    };
};
