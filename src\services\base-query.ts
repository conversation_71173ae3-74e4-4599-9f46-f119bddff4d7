import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getToken } from 'features/Auth/authSlice';
import { RootState } from '@/store/store';

const baseQuery = fetchBaseQuery({
    baseUrl: 'http://localhost:8000',
    prepareHeaders: (headers, { getState }) => {
        const state = getState() as RootState
        const token = getToken(state);

        if (token) {
            headers.set('Authorization', `Bearer ${token}`);
        }

        return headers;
    }
});

export default baseQuery;
