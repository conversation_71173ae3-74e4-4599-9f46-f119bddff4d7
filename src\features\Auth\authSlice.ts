import { RootState } from '@/store/store';
import { createSelector, createSlice } from '@reduxjs/toolkit';
import { getSessionStorageItem, setSessionStorageItem, removeSessionStorageItem } from '../utils/sessionStorage';

interface InitialState {
    uuid: string | null;
    token: string | null;
}

const initialState: InitialState = {
    uuid: getSessionStorageItem('uuid') || null,
    token: getSessionStorageItem('token') || null,
};

export const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        unsetCredentials: (state) => {
            state.uuid = null;
            state.token = null;

            removeSessionStorageItem('token');
            removeSessionStorageItem('uuid');
        },
        setCredentials: (state, action) => {
            const { uuid, token } = action.payload;

            if (!token) {
                return;
            }

            state.uuid = uuid;
            state.token = token;

            setSessionStorageItem('token', token);
            setSessionStorageItem('uuid', uuid);
        }
    }
});

export const { actions, name } = authSlice

export const { unsetCredentials, setCredentials } = actions;

export const getCredentials = (state: RootState) => state[name];

export const getToken = createSelector(getCredentials, state => state?.token ?? null);

export const getUUID = createSelector(getCredentials, state => state?.uuid ?? null);
